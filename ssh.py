import os
import csv

# 原始数据（从表格中复制）
data = """
| POD2-GPU32    | 10.102.11.32    |
| POD2-GPU65    | 10.102.11.65    |
| POD2-GPU66    | 10.102.11.66    |
| POD2-GPU67    | 10.102.11.67    |
| POD2-GPU68    | 10.102.11.68    |
| POD2-GPU69    | 10.102.11.69    |
| POD2-GPU70    | 10.102.11.70    |
| POD2-GPU71    | 10.102.11.71    |
| POD2-GPU72    | 10.102.11.72    |
| POD2-GPU73    | 10.102.11.73    |
| POD2-GPU74    | 10.102.11.74    |
| POD2-GPU75    | 10.102.11.75    |
| POD2-GPU76    | 10.102.11.76    |
| POD2-GPU77    | 10.102.11.77    |
| POD2-GPU78    | 10.102.11.78    |
| POD2-GPU79    | 10.102.11.79    |
| POD2-GPU80    | 10.102.11.80    |
| POD2-GPU81    | 10.102.11.81    |
| POD2-GPU83    | 10.102.11.83    |
| POD2-GPU84    | 10.102.11.84    |
| POD2-GPU85    | 10.102.11.85    |
| POD2-GPU86    | ************    |
| POD2-GPU87    | ************    |
| POD2-GPU88    | ************    |
| POD2-GPU89    | ************    |
| POD2-GPU93    | ************    |
| POD2-GPU98    | ************    |
| POD2-GPU101    | *************    |
| POD2-GPU110    | *************    |
| POD2-GPU116    | *************    |
| POD2-GPU117    | *************    |
| POD2-GPU118    | *************    |
"""

def create_xshell_csv():
    # 解析表格数据
    sessions = []
    for line in data.strip().split('\n'):
        parts = [p.strip() for p in line.split('|') if p.strip()]
        if len(parts) >= 2:
            name = parts[0]
            ip = parts[1]
            sessions.append((name, ip))
    
    # 创建输出目录
    output_dir = "XShell_Import"
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成XShell导入CSV文件
    csv_filename = os.path.join(output_dir, "xshell_sessions.csv")
    
    # XShell CSV格式的列头
    headers = [
        "Name",           # 会话名称
        "Protocol",       # 协议
        "Host",           # 主机地址
        "Port",           # 端口
        "Username",       # 用户名
        "Password",       # 密码
        "Description",    # 描述
        "Folder"          # 文件夹路径
    ]
    
    with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)
        # 写入列头
        writer.writerow(headers)
        
        # 写入每个会话的数据
        for name, ip in sessions:
            row = [
                name,               # Name
                "SSH",              # Protocol
                ip,                 # Host
                "22",               # Port
                "kgzs",             # Username
                "Kgzs@2024",             # Password
                f"GPU Server {name}",  # Description
                "POD2-GPU"          # Folder (会话分组)
            ]
            writer.writerow(row)
    
    # 生成导入说明文件
    readme_file = os.path.join(output_dir, "导入说明.txt")
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write("XShell会话导入说明\n")
        f.write("==================\n\n")
        f.write("导入步骤：\n")
        f.write("1. 打开XShell\n")
        f.write("2. 点击菜单栏：工具 -> 导入\n")
        f.write("3. 选择 'xshell_sessions.csv' 文件\n")
        f.write("4. 按照向导完成导入\n\n")
        f.write("注意事项：\n")
        f.write("- 所有服务器的用户名为: root\n")
        f.write("- 所有服务器的密码为: root\n")
        f.write("- 所有会话将被导入到 'POD2-GPU' 文件夹下\n")
        f.write(f"- 共计 {len(sessions)} 个会话\n")
    
    print(f"已创建XShell导入CSV文件")
    print(f"文件位置: {os.path.abspath(csv_filename)}")
    print(f"共计 {len(sessions)} 个会话")
    print("\n导入方法：")
    print("1. 打开XShell")
    print("2. 点击菜单栏：工具 -> 导入")
    print("3. 选择生成的 'xshell_sessions.csv' 文件")
    print("4. 按照向导完成导入")

if __name__ == "__main__":
    create_xshell_csv()