#!/bin/bash

# 账号密码配置
USERNAME="kgzs"
PASSWORD="Kgzs@2024"

# 源文件
SOURCE_FILE="./resources.zip"

# 目标目录
TARGET_DIR="/home/<USER>"

# IP地址列表
IPS=(
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "************"
    "*************"
    "*************"
    "*************"
    "*************"
    "*************"
)

# 检查源文件是否存在
if [ ! -f "$SOURCE_FILE" ]; then
    echo "错误: 文件 $SOURCE_FILE 不存在"
    exit 1
fi

# 检查sshpass是否安装
if ! command -v sshpass &> /dev/null; then
    echo "错误: sshpass 未安装"
    echo "请先安装 sshpass:"
    echo "  Ubuntu/Debian: sudo apt-get install sshpass"
    echo "  CentOS/RHEL: sudo yum install sshpass"
    exit 1
fi

# 成功和失败计数
SUCCESS_COUNT=0
FAIL_COUNT=0
FAILED_IPS=()

echo "开始批量传输文件..."
echo "源文件: $SOURCE_FILE"
echo "目标路径: $TARGET_DIR"
echo "服务器总数: ${#IPS[@]}"
echo "================================"

# 遍历所有IP地址
for IP in "${IPS[@]}"; do
    echo -n "正在传输到 $IP ... "
    
    # 使用sshpass和scp传输文件
    # -o StrictHostKeyChecking=no 跳过主机密钥验证
    # -o UserKnownHostsFile=/dev/null 不保存主机密钥
    sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null \
        "$SOURCE_FILE" "$USERNAME@$IP:$TARGET_DIR/" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "成功"
        ((SUCCESS_COUNT++))
    else
        echo "失败"
        ((FAIL_COUNT++))
        FAILED_IPS+=($IP)
    fi
done

echo "================================"
echo "传输完成!"
echo "成功: $SUCCESS_COUNT 台服务器"
echo "失败: $FAIL_COUNT 台服务器"

# 如果有失败的，显示失败的IP列表
if [ $FAIL_COUNT -gt 0 ]; then
    echo ""
    echo "传输失败的服务器列表:"
    for IP in "${FAILED_IPS[@]}"; do
        echo "  - $IP"
    done
fi