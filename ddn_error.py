import subprocess
import datetime
import re
import os

def get_lustre_dmesg(host):
    try:
        cmd = f"ssh root@{host} 'dmesg -T | grep -i lustre'"
        output = subprocess.check_output(cmd, shell=True).decode()

        if output:
            log_entries = []
            for line in output.splitlines():
                if line.strip():
                    log_entries.append((host, line))
            return log_entries
        return []

    except Exception as e:
        return [("ERROR", f"=== Error connecting to {host}: {str(e)} ===")]

def parse_timestamp(log_line):
    timestamp_pattern = r'\[([^\]]+)\]'
    match = re.search(timestamp_pattern, log_line)

    if match:
        timestamp_str = match.group(1)
        try:
            dt = datetime.datetime.strptime(timestamp_str, "%a %b %d %H:%M:%S %Y")
            return dt
        except ValueError:
            try:
                dt = datetime.datetime.strptime(timestamp_str, "%a %b  %d %H:%M:%S %Y")
                return dt
            except ValueError:
                return datetime.datetime.min

    return datetime.datetime.min

def get_last_timestamp_from_log(log_file_path):
    if not os.path.exists(log_file_path):
        return None

    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        for line in reversed(lines):
            line = line.strip()
            if not line or line.startswith('==='):
                continue

            if '[' in line and ']' in line:
                timestamp = parse_timestamp(line)
                if timestamp != datetime.datetime.min:
                    return timestamp

        return None
    except Exception as e:
        print(f"读取日志文件时出错: {e}")
        return None

def filter_new_logs(log_entries, last_timestamp):
    if last_timestamp is None:
        return log_entries

    new_entries = []
    for host, log_line in log_entries:
        if host == "ERROR":
            new_entries.append((host, log_line))
            continue

        log_timestamp = parse_timestamp(log_line)
        if log_timestamp > last_timestamp:
            new_entries.append((host, log_line))

    return new_entries

def main():
    hosts = [f"10.100.165.{i}" for i in range(212, 224)]
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_file_path = "ddn_error.log"

    last_timestamp = get_last_timestamp_from_log(log_file_path)

    if last_timestamp:
        print(f"上次记录的最后时间戳: {last_timestamp}")
        print("正在收集增量日志...")
    else:
        print("未找到历史日志或首次运行，将收集所有日志...")

    all_log_entries = []

    for host in hosts:
        entries = get_lustre_dmesg(host)
        all_log_entries.extend(entries)

    new_log_entries = filter_new_logs(all_log_entries, last_timestamp)

    if not new_log_entries:
        print("没有发现新的日志条目。")
        return

    print(f"发现 {len(new_log_entries)} 条新日志条目。")

    def sort_key(entry):
        host, log_line = entry
        if host == "ERROR":
            return datetime.datetime.max
        return parse_timestamp(log_line)

    new_log_entries.sort(key=sort_key)

    with open(log_file_path, "a", encoding='utf-8') as f:
        f.write(f"\n=== Lustre dmesg logs collected at {current_time} ===\n")
        for host, log_line in new_log_entries:
            if host == "ERROR":
                f.write(f"{log_line}\n")
            else:
                f.write(f"{host} {log_line}\n")

    print(f"新日志已追加到 {log_file_path}")

    if new_log_entries:
        timestamps = []
        for host, log_line in new_log_entries:
            if host != "ERROR":
                ts = parse_timestamp(log_line)
                if ts != datetime.datetime.min:
                    timestamps.append(ts)

        if timestamps:
            timestamps.sort()
            print(f"新日志时间范围: {timestamps[0]} 到 {timestamps[-1]}")

if __name__ == "__main__":
    main()